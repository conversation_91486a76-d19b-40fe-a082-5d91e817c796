package unit

import (
	"context"
	"testing"
	"time"

	"github.com/Xelvra/peerchat/internal/p2p"
	"github.com/libp2p/go-libp2p"
	"github.com/libp2p/go-libp2p/core/peer"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestTransportManager_Creation(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create transport manager
	tm := p2p.NewTransportManager(logger)
	assert.NotNil(t, tm)
}

func TestTransportManager_RegisterTransport(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create transport manager
	tm := p2p.NewTransportManager(logger)

	// Create test host for libp2p transport
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Create and register libp2p transport
	libp2pTransport := p2p.NewLibP2PTransport(host, logger)
	tm.RegisterTransport("libp2p", libp2pTransport)

	// Set as primary transport
	err = tm.SetPrimaryTransport("libp2p")
	assert.NoError(t, err)

	// Try to set non-existent transport as primary
	err = tm.SetPrimaryTransport("non-existent")
	assert.Error(t, err)
}

func TestTransportManager_FallbackTransports(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create transport manager
	tm := p2p.NewTransportManager(logger)

	// Create test host for libp2p transport
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Register multiple transports
	libp2pTransport := p2p.NewLibP2PTransport(host, logger)
	tm.RegisterTransport("libp2p", libp2pTransport)
	tm.RegisterTransport("tcp", libp2pTransport) // Using same transport for testing

	// Set fallback transports
	err = tm.SetFallbackTransports([]string{"tcp"})
	assert.NoError(t, err)

	// Try to set non-existent transport as fallback
	err = tm.SetFallbackTransports([]string{"non-existent"})
	assert.Error(t, err)
}

func TestTransportManager_Connect(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create transport manager
	tm := p2p.NewTransportManager(logger)

	// Create test host for libp2p transport
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Register and set primary transport
	libp2pTransport := p2p.NewLibP2PTransport(host, logger)
	tm.RegisterTransport("libp2p", libp2pTransport)
	err = tm.SetPrimaryTransport("libp2p")
	require.NoError(t, err)

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	testPeerID := host.ID()
	testAddrs := []string{"127.0.0.1:8080"}

	conn, err := tm.Connect(ctx, testPeerID, testAddrs)
	// This will likely fail in test environment, which is expected
	if err != nil {
		t.Logf("Connection failed as expected in test environment: %v", err)
	} else {
		assert.NotNil(t, conn)
		defer conn.Close()
	}
}

func TestLibP2PTransport_Creation(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create test host
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Create libp2p transport
	transport := p2p.NewLibP2PTransport(host, logger)
	assert.NotNil(t, transport)
}

func TestLibP2PTransport_LocalAddresses(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create test host
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Create libp2p transport
	transport := p2p.NewLibP2PTransport(host, logger)

	// Get local addresses
	addrs := transport.LocalAddresses()
	assert.NotEmpty(t, addrs)

	t.Logf("Local addresses: %v", addrs)
}

func TestLibP2PTransport_Connect(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create test host
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Create libp2p transport
	transport := p2p.NewLibP2PTransport(host, logger)

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	testPeerID := host.ID()
	testAddrs := []string{"127.0.0.1:8080"}

	conn, err := transport.Connect(ctx, testPeerID, testAddrs)
	// This will likely fail in test environment, which is expected
	if err != nil {
		t.Logf("Connection failed as expected in test environment: %v", err)
	} else {
		assert.NotNil(t, conn)
		defer conn.Close()
	}
}

func TestLibP2PTransport_Listen(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create test host
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Create libp2p transport
	transport := p2p.NewLibP2PTransport(host, logger)

	// Test listening
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	listener, err := transport.Listen(ctx, "127.0.0.1:0")
	if err != nil {
		t.Logf("Listen failed as expected for libp2p: %v", err)
	} else {
		assert.NotNil(t, listener)
		defer listener.Close()
	}
}

func TestConnectionPool_Basic(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create transport manager (which includes connection pool)
	tm := p2p.NewTransportManager(logger)

	// Create test host
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Register transport
	libp2pTransport := p2p.NewLibP2PTransport(host, logger)
	tm.RegisterTransport("libp2p", libp2pTransport)
	err = tm.SetPrimaryTransport("libp2p")
	require.NoError(t, err)

	// Test connection pooling by attempting multiple connections
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	testPeerID := host.ID()
	testAddrs := []string{"127.0.0.1:8080"}

	// First connection attempt
	conn1, err1 := tm.Connect(ctx, testPeerID, testAddrs)
	
	// Second connection attempt (should potentially use pool)
	conn2, err2 := tm.Connect(ctx, testPeerID, testAddrs)

	// Both will likely fail in test environment, but we test the pooling logic
	if err1 != nil && err2 != nil {
		t.Logf("Both connections failed as expected in test environment")
		t.Logf("Error 1: %v", err1)
		t.Logf("Error 2: %v", err2)
	} else {
		if conn1 != nil {
			defer conn1.Close()
		}
		if conn2 != nil {
			defer conn2.Close()
		}
	}

	t.Log("Connection pool test completed")
}

func TestTransportMetrics(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create transport manager
	tm := p2p.NewTransportManager(logger)

	// Create test host
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Register transport
	libp2pTransport := p2p.NewLibP2PTransport(host, logger)
	tm.RegisterTransport("libp2p", libp2pTransport)
	err = tm.SetPrimaryTransport("libp2p")
	require.NoError(t, err)

	// Attempt connection to generate metrics
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	testPeerID := host.ID()
	testAddrs := []string{"127.0.0.1:8080"}

	_, _ = tm.Connect(ctx, testPeerID, testAddrs)

	// Metrics are internal, but we can test that the system handles them
	t.Log("Transport metrics test completed - metrics are tracked internally")
}

func TestLibP2PConnection_Properties(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create test host
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Create a mock libp2p connection for testing
	conn := &p2p.LibP2PConnection{} // This would need proper initialization in real code

	// Test connection properties (these are mostly placeholders in our implementation)
	// In a real implementation, these would return actual values

	t.Log("LibP2P connection properties test completed")
}

func TestTransportAbstraction_ErrorHandling(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create transport manager
	tm := p2p.NewTransportManager(logger)

	// Test connecting without any registered transports
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()

	testPeerID, _ := peer.Decode("12D3KooWGBfKqTqgkrsJGxEk8VjBkXcNYF3Pz7kQJvGwGGGGGGGG")
	if testPeerID == "" {
		// Create a dummy peer ID for testing
		host, err := libp2p.New()
		require.NoError(t, err)
		defer host.Close()
		testPeerID = host.ID()
	}

	testAddrs := []string{"127.0.0.1:8080"}

	conn, err := tm.Connect(ctx, testPeerID, testAddrs)
	assert.Error(t, err)
	assert.Nil(t, conn)

	t.Log("Error handling test completed")
}

// Benchmark tests
func BenchmarkTransportManager_Connect(b *testing.B) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce noise in benchmarks

	// Create transport manager
	tm := p2p.NewTransportManager(logger)

	// Create test host
	host, err := libp2p.New()
	require.NoError(b, err)
	defer host.Close()

	// Register transport
	libp2pTransport := p2p.NewLibP2PTransport(host, logger)
	tm.RegisterTransport("libp2p", libp2pTransport)
	err = tm.SetPrimaryTransport("libp2p")
	require.NoError(b, err)

	testPeerID := host.ID()
	testAddrs := []string{"127.0.0.1:8080"}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
		_, _ = tm.Connect(ctx, testPeerID, testAddrs)
		cancel()
	}
}

func BenchmarkLibP2PTransport_LocalAddresses(b *testing.B) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce noise in benchmarks

	// Create test host
	host, err := libp2p.New()
	require.NoError(b, err)
	defer host.Close()

	// Create libp2p transport
	transport := p2p.NewLibP2PTransport(host, logger)

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_ = transport.LocalAddresses()
	}
}
