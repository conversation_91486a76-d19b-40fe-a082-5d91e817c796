package unit

import (
	"context"
	"testing"
	"time"

	"github.com/Xelvra/peerchat/internal/p2p"
	"github.com/libp2p/go-libp2p"
	"github.com/libp2p/go-libp2p/core/peer"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAdvancedDHT_Creation(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create test host
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Create advanced DHT
	dht := p2p.NewAdvancedDHT(host, logger)
	assert.NotNil(t, dht)

	// Check initial status
	status := dht.GetStatus()
	assert.False(t, status.Active)
	assert.Equal(t, 0, status.ConnectedPeers)
	assert.Equal(t, 0, status.BucketCount)
}

func TestAdvancedDHT_StartStop(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create test host
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Create and start advanced DHT
	dht := p2p.NewAdvancedDHT(host, logger)
	
	err = dht.Start()
	assert.NoError(t, err)

	// Check status after start
	status := dht.GetStatus()
	assert.True(t, status.Active)

	// Stop DHT
	err = dht.Stop()
	assert.NoError(t, err)

	// Check status after stop
	status = dht.GetStatus()
	assert.False(t, status.Active)
}

func TestAdvancedDHT_FindPeers(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create test host
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Create and start advanced DHT
	dht := p2p.NewAdvancedDHT(host, logger)
	err = dht.Start()
	require.NoError(t, err)
	defer dht.Stop()

	// Test finding peers
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	peers, err := dht.FindPeers(ctx, "test-namespace", 5)
	// This might fail in test environment without network, that's expected
	if err != nil {
		t.Logf("FindPeers failed as expected in test environment: %v", err)
	} else {
		assert.LessOrEqual(t, len(peers), 5)
	}
}

func TestAdvancedDHT_BatteryOptimization(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create test host
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Create and start advanced DHT
	dht := p2p.NewAdvancedDHT(host, logger)
	err = dht.Start()
	require.NoError(t, err)
	defer dht.Stop()

	// Wait a bit for initialization
	time.Sleep(100 * time.Millisecond)

	// Check battery optimization status
	status := dht.GetStatus()
	assert.False(t, status.BatteryOptimized) // Should start in normal mode

	// Test would need battery level simulation for full testing
	t.Log("Battery optimization test completed - full testing requires battery simulation")
}

func TestAdvancedDHT_PeerMetrics(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create test host
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Create and start advanced DHT
	dht := p2p.NewAdvancedDHT(host, logger)
	err = dht.Start()
	require.NoError(t, err)
	defer dht.Stop()

	// Test peer metrics would require actual peer interactions
	// This is a placeholder for metrics testing
	t.Log("Peer metrics test - would require actual peer interactions")
}

func TestAdvancedDHT_AdaptiveTimeouts(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create test host
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Create and start advanced DHT
	dht := p2p.NewAdvancedDHT(host, logger)
	err = dht.Start()
	require.NoError(t, err)
	defer dht.Stop()

	// Test adaptive timeouts
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Generate a random peer ID for testing
	testPeerID, err := peer.Decode("12D3KooWGBfKqTqgkrsJGxEk8VjBkXcNYF3Pz7kQJvGwGGGGGGGG")
	if err != nil {
		// Create a simple peer ID for testing
		testPeerID = host.ID() // Use host's own ID for testing
	}

	// Test FindPeer with adaptive timeout
	_, err = dht.FindPeer(ctx, testPeerID)
	// This will likely fail in test environment, which is expected
	if err != nil {
		t.Logf("FindPeer failed as expected in test environment: %v", err)
	}

	t.Log("Adaptive timeout test completed")
}

func TestAdvancedDHT_BucketManagement(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create test host
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Create and start advanced DHT
	dht := p2p.NewAdvancedDHT(host, logger)
	err = dht.Start()
	require.NoError(t, err)
	defer dht.Stop()

	// Wait for initialization
	time.Sleep(200 * time.Millisecond)

	// Check bucket count
	status := dht.GetStatus()
	assert.Equal(t, 256, status.BucketCount) // Should have 256 Kademlia buckets

	t.Log("Bucket management test completed")
}

func TestAdvancedDHT_NetworkQuality(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create test host
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Create and start advanced DHT
	dht := p2p.NewAdvancedDHT(host, logger)
	err = dht.Start()
	require.NoError(t, err)
	defer dht.Stop()

	// Wait for initialization
	time.Sleep(100 * time.Millisecond)

	// Check initial network quality
	status := dht.GetStatus()
	assert.Equal(t, 1.0, status.NetworkQuality) // Should start with perfect quality

	t.Log("Network quality test completed")
}

func TestAdvancedDHT_Maintenance(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create test host
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Create and start advanced DHT
	dht := p2p.NewAdvancedDHT(host, logger)
	err = dht.Start()
	require.NoError(t, err)
	defer dht.Stop()

	// Wait for at least one maintenance cycle
	time.Sleep(1100 * time.Millisecond) // Maintenance runs every minute

	// Check that DHT is still active
	status := dht.GetStatus()
	assert.True(t, status.Active)

	t.Log("Maintenance test completed")
}

func TestAdvancedDHT_Advertise(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create test host
	host, err := libp2p.New()
	require.NoError(t, err)
	defer host.Close()

	// Create and start advanced DHT
	dht := p2p.NewAdvancedDHT(host, logger)
	err = dht.Start()
	require.NoError(t, err)
	defer dht.Stop()

	// Test advertising
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err = dht.Advertise(ctx, "test-namespace")
	// This might fail in test environment without network, that's expected
	if err != nil {
		t.Logf("Advertise failed as expected in test environment: %v", err)
	}

	t.Log("Advertise test completed")
}

// Benchmark tests
func BenchmarkAdvancedDHT_FindPeers(b *testing.B) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce noise in benchmarks

	// Create test host
	host, err := libp2p.New()
	require.NoError(b, err)
	defer host.Close()

	// Create and start advanced DHT
	dht := p2p.NewAdvancedDHT(host, logger)
	err = dht.Start()
	require.NoError(b, err)
	defer dht.Stop()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
		_, _ = dht.FindPeers(ctx, "benchmark-test", 1)
		cancel()
	}
}

func BenchmarkAdvancedDHT_Advertise(b *testing.B) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce noise in benchmarks

	// Create test host
	host, err := libp2p.New()
	require.NoError(b, err)
	defer host.Close()

	// Create and start advanced DHT
	dht := p2p.NewAdvancedDHT(host, logger)
	err = dht.Start()
	require.NoError(b, err)
	defer dht.Stop()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
		_ = dht.Advertise(ctx, "benchmark-test")
		cancel()
	}
}
