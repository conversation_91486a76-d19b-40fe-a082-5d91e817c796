# Changelog

All notable changes to the Xelvra P2P Messenger project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

## [0.3.0-alpha] - 2024-12-17

### Added
- **Proof-of-Work Identity System**: Sybil-resistant DID creation with configurable difficulty
- **Hierarchical Reputation System**: 5-level trust progression (Ghost → User → Architect → Ambassador → God)
- **Hierarchical Peer Discovery**: Local-first discovery with mDNS/UDP priority, DHT fallback
- **Energy Optimization System**: Battery-aware operations with adaptive polling and deep sleep mode
- **LRU Caching**: Intelligent local peer caching for performance optimization
- **Resource Monitoring**: Real-time CPU and memory usage tracking
- **Trust-based Rate Limiting**: Message limits based on user reputation level
- **Peer Verification System**: Network-based trust building mechanism

### Enhanced
- P2P Node architecture with integrated energy management
- Discovery Manager with hierarchical approach and local priority
- User Identity system with PoW validation integration
- CLI Status command with energy and reputation information
- Security with memory protection for cryptographic keys

### Technical
- New APIs: EnergyManager, ReputationManager, ProofOfWork
- Enhanced DiscoveryManager with local-first approach
- Comprehensive unit test placeholders for all new components
- Updated documentation reflecting new architecture

### Performance
- Memory usage optimization (<20MB target maintained)
- CPU usage optimization (<1% idle target maintained)
- Network latency optimization (<50ms target maintained)
- Battery consumption profiling and optimization

## [0.2.0-alpha] - 2025-06-16

### Added
- **Enhanced Interactive Chat Experience**:
  - Tab completion for commands and peer IDs
  - Command history navigation with ↑/↓ arrow keys
  - Full readline support with keyboard shortcuts (Ctrl+C, Ctrl+L, Ctrl+A, Ctrl+E)
  - Command history search with Ctrl+R
- **New Interactive Commands**:
  - `/clear` - Clear screen command
  - `/disconnect <peer_id>` - Disconnect from specific peer
- **Improved Network Diagnostics**:
  - Real NAT type detection (port_restricted, etc.)
  - Public IP address display
  - Enhanced peer discovery with multiple methods
  - Real-time connection quality assessment

### Fixed
- All GitHub Actions compilation errors resolved
- Code formatting compliance with gofmt standards
- Enhanced error handling and graceful failure management
- Memory safety improvements with better resource cleanup
- Linting integration with comprehensive code quality checks

### Enhanced
- CLI Manual with complete interactive features documentation
- Keyboard shortcuts guide and commands reference
- Context-aware help system in interactive mode
- Professional documentation standards for GitHub
- Multi-instance support on same machine with different ports

### Performance
- Interactive responsiveness <10ms input handling
- Automatic transport selection based on network conditions
- Enhanced error logging and debugging capabilities

## [0.1.0-alpha] - 2025-06-15

### Added
- **Initial Project Setup**:
  - Complete directory structure and build system
  - Go module initialization with libp2p dependencies
  - Cross-platform build scripts and CI/CD pipeline

- **Core P2P Infrastructure**:
  - libp2p integration with QUIC/TCP transports
  - P2P node initialization and graceful shutdown
  - Real-time P2P networking capabilities

- **Network Discovery & Connectivity**:
  - mDNS peer discovery implementation
  - UDP broadcast discovery for local networks
  - STUN integration for NAT traversal
  - Public IP detection and NAT type identification

- **Transport Layer**:
  - QUIC transport as primary protocol (UDP/QUIC-v1)
  - TCP fallback for reliability
  - UDP buffer optimization

- **Messaging & File Transfer**:
  - P2P message sending and receiving
  - Interactive chat mode with basic functionality
  - Secure P2P file transfer with chunking
  - Progress tracking and resume capability

- **Complete CLI Application** with 12 commands:
  - `init` - Identity generation and configuration
  - `start` - Interactive P2P chat mode
  - `listen` - Passive message monitoring
  - `send` - P2P message transmission
  - `send-file` - Secure file transfer
  - `connect` - Peer connection management
  - `discover` - Network peer discovery
  - `status` - Real-time node status and diagnostics
  - `doctor` - Comprehensive network diagnostics
  - `manual` - Complete usage documentation
  - `id` - Identity information display
  - `profile` - Peer information lookup

- **Security & Identity**:
  - MessengerID generation (DID format preparation)
  - Cryptographic identity management
  - Secure configuration directory creation

- **AI-Driven Features**:
  - Machine learning optimization for transport selection
  - Intelligent peer discovery and connection management
  - Adaptive network prediction algorithms

- **Logging & Diagnostics**:
  - Comprehensive logging system with file rotation
  - Log rotation (5MB/10k lines, 3 backup files)
  - Network diagnostics with detailed reporting
  - Real-time status monitoring

- **Documentation & Infrastructure**:
  - Complete GitHub repository setup
  - Comprehensive documentation in `/docs` directory
  - GitHub Actions CI/CD pipeline
  - Issue templates and contribution guidelines
  - Professional README and installation guides

### Performance Targets Achieved
- Memory usage: <20MB idle
- CPU usage: <1% idle
- Message latency: <50ms direct connections
- File transfer: Chunked, resumable, secure
- Network discovery: Multi-method, real-time

### Testing
- Unit tests implementation and validation
- File transfer testing with chunking and resume
- Live P2P communication testing between multiple instances
- Network discovery testing across different networks
- All CLI commands functionality verification
- AI-driven routing and transport optimization testing

## [0.0.1] - 2024-12-15

### Added
- Initial project conception and planning
- Basic project structure definition
- Technology stack selection (Go, libp2p, QUIC)
- Development environment setup
- Initial documentation framework

---

## Release Links

- [v0.3.0-alpha](https://github.com/Xelvra/peerchat/releases/tag/v0.3.0-alpha)
- [v0.2.0-alpha](https://github.com/Xelvra/peerchat/releases/tag/v0.2.0-alpha)
- [v0.1.0-alpha](https://github.com/Xelvra/peerchat/releases/tag/v0.1.0-alpha)

## License

This project is licensed under the GNU Affero General Public License v3.0 (AGPLv3).
